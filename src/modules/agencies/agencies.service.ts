import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Agency } from '@/entities/agency.entity';
import { CreateAgencyDto } from './dto/create-agency.dto';
import { UpdateAgencyDto } from './dto/update-agency.dto';
import { BulkCreateAgencyDto, BulkCreateAgencyResponseDto } from './dto/bulk-create-agency.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class AgenciesService {
  constructor(
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createAgencyDto: CreateAgencyDto, createdBy?: string): Promise<Agency> {
    this.logger.info(
      `Creating new agency: ${createAgencyDto.name}`,
      'AgenciesService',
      { name: createAgencyDto.name, code: createAgencyDto.code, createdBy }
    );

    try {
      // Check if agency code already exists
      const existingAgency = await this.agencyRepository.findOne({
        where: { code: createAgencyDto.code },
      });
      if (existingAgency) {
        throw new ConflictException(`Agency code ${createAgencyDto.code} already exists`);
      }

      const agency = this.agencyRepository.create({
        ...createAgencyDto,
        createdBy,
      });

      const savedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency created successfully: ${savedAgency.code}`,
        'AgenciesService',
        { agencyId: savedAgency.id, name: savedAgency.code, code: savedAgency.code }
      );

      return savedAgency;
    } catch (error) {
      this.logger.error(
        `Failed to create agency: ${createAgencyDto.name}`,
        error,
        'AgenciesService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Agency>> {
    this.logger.info('Fetching all agencies', 'AgenciesService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.agencyRepository.createQueryBuilder('agency')
        .leftJoinAndSelect('agency.brand', 'brand');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`agency.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('agency.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [agencies, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${agencies.length} agencies`,
        'AgenciesService',
        { total, page, limit }
      );

      return createPaginatedResult(agencies, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch agencies', error, 'AgenciesService');
      throw error;
    }
  }

  async findOne(id: string): Promise<Agency> {
    this.logger.info(`Fetching agency by ID: ${id}`, 'AgenciesService');

    try {
      const agency = await this.agencyRepository.findOne({
        where: { id },
        relations: ['members', 'brand'],
      });

      if (!agency) {
        this.logger.warn(`Agency not found: ${id}`, 'AgenciesService');
        throw new NotFoundException('Agency not found');
      }

      this.logger.info(`Agency found: ${agency.code}`, 'AgenciesService', { agencyId: id });
      return agency;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async update(id: string, updateAgencyDto: UpdateAgencyDto, updatedBy?: string): Promise<Agency> {
    this.logger.info(`Updating agency: ${id}`, 'AgenciesService', { updateAgencyDto, updatedBy });

    try {
      const agency = await this.findOne(id);

      // Check if new code conflicts with existing agency (if code is being updated)
      if (updateAgencyDto.code && updateAgencyDto.code !== agency.code) {
        const existingAgency = await this.agencyRepository.findOne({
          where: { code: updateAgencyDto.code },
        });
        if (existingAgency) {
          throw new ConflictException(`Agency code ${updateAgencyDto.code} already exists`);
        }
      }

      // Update agency
      Object.assign(agency, updateAgencyDto, { updatedBy });
      const updatedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency updated successfully: ${updatedAgency.code}`,
        'AgenciesService',
        { agencyId: id }
      );

      return updatedAgency;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to update agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting agency: ${id}`, 'AgenciesService', { deletedBy });

    try {
      const agency = await this.findOne(id);

      // Soft delete
      agency.deletedBy = deletedBy;
      await this.agencyRepository.softDelete(id);

      this.logger.info(`Agency deleted successfully: ${agency.code}`, 'AgenciesService', { agencyId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete agency: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  async toggleActive(id: string, updatedBy?: string): Promise<Agency> {
    this.logger.info(`Toggling agency active status: ${id}`, 'AgenciesService', { updatedBy });

    try {
      const agency = await this.findOne(id);
      agency.updatedBy = updatedBy;

      const updatedAgency = await this.agencyRepository.save(agency);

      this.logger.info(
        `Agency active status toggled: ${updatedAgency.code} - ${updatedAgency.code}`,
        'AgenciesService',
        { agencyId: id, active: updatedAgency.code }
      );

      return updatedAgency;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to toggle agency active status: ${id}`, error, 'AgenciesService');
      throw error;
    }
  }

  /**
   * Bulk create agencies with optimized performance
   */
  async bulkCreate(bulkCreateDto: BulkCreateAgencyDto, createdBy?: string): Promise<BulkCreateAgencyResponseDto> {
    this.logger.info(
      `Starting bulk create for ${bulkCreateDto.agencies.length} agencies`,
      'AgenciesService',
      { count: bulkCreateDto.agencies.length, createdBy }
    );

    const response: BulkCreateAgencyResponseDto = {
      success: 0,
      failed: 0,
      total: bulkCreateDto.agencies.length,
      errors: [],
      createdIds: [],
    };

    // Use transaction for better performance and data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate all codes are unique in the batch
      const codes = bulkCreateDto.agencies.map(a => a.code);
      const duplicateCodes = codes.filter((code, index) => codes.indexOf(code) !== index);

      if (duplicateCodes.length > 0) {
        response.errors.push(`Duplicate agency codes in batch: ${duplicateCodes.join(', ')}`);
      }

      // Check for existing codes in database
      const existingAgencies = await queryRunner.manager.find(Agency, {
        where: codes.map(code => ({ code })),
        select: ['code'],
      });

      const existingCodes = existingAgencies.map(a => a.code);

      // Filter valid agencies
      const validAgencies = bulkCreateDto.agencies.filter(agency => {
        const isCodeValid = !existingCodes.includes(agency.code) && !duplicateCodes.includes(agency.code);

        if (!isCodeValid) {
          response.errors.push(`Agency code ${agency.code} already exists or is duplicate`);
          response.failed++;
          return false;
        }

        return true;
      });

      if (validAgencies.length > 0) {
        // Prepare agencies data for bulk insert
        const agenciesToCreate = validAgencies.map(agencyData => {
          const agencyEntity: any = {
            brand_id: agencyData.brand_id,
            code: agencyData.code,
            name: agencyData.name,
            active: agencyData.active ?? true,
            createdBy,
          };

          if (agencyData.email) {
            agencyEntity.email = agencyData.email;
          }
          if (agencyData.phone) {
            agencyEntity.phone = agencyData.phone;
          }
          if (agencyData.address) {
            agencyEntity.address = agencyData.address;
          }
          if (agencyData.extra) {
            agencyEntity.extra = agencyData.extra;
          }
          if (agencyData.description) {
            agencyEntity.description = agencyData.description;
          }

          return agencyEntity;
        });

        // Use bulk insert for better performance
        const insertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(Agency)
          .values(agenciesToCreate)
          .execute();

        response.success = validAgencies.length;
        response.createdIds = insertResult.identifiers.map(identifier => identifier.id);

        this.logger.info(
          `Successfully bulk created ${response.success} agencies`,
          'AgenciesService',
          { success: response.success, failed: response.failed }
        );
      }

      await queryRunner.commitTransaction();

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        'Failed to bulk create agencies',
        error,
        'AgenciesService'
      );

      // If bulk insert fails, add error to response
      response.failed = bulkCreateDto.agencies.length;
      response.success = 0;
      response.errors.push(`Bulk insert failed: ${error.message}`);

    } finally {
      await queryRunner.release();
    }

    return response;
  }
}
