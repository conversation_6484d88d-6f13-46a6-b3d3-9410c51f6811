import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MemberStatistics } from '@/entities/member-statistics.entity';
import { Agency } from '@/entities/agency.entity';
import { MemberStatisticsService } from './member-statistics.service';
import { MemberStatisticsController } from './member-statistics.controller';
import { CommonServicesModule } from '@/common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MemberStatistics, Agency]),
    CommonServicesModule,
  ],
  controllers: [MemberStatisticsController],
  providers: [MemberStatisticsService],
  exports: [MemberStatisticsService],
})
export class MemberStatisticsModule {}
