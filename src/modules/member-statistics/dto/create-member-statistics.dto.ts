import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsDateString, IsUUID } from 'class-validator';

export class CreateMemberStatisticsDto {
  @ApiPropertyOptional({
    description: 'Brand ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  brand_id?: string;

  @ApiPropertyOptional({
    description: 'Agency code',
    example: 'AG001',
  })
  @IsOptional()
  @IsString()
  agency_code?: string;

  @ApiPropertyOptional({
    description: 'Username',
    example: 'john_doe',
  })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({
    description: 'Number of deposit times',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  deposit_times?: number;

  @ApiPropertyOptional({
    description: 'Total deposit amount',
    example: 1000.50,
  })
  @IsOptional()
  @IsNumber()
  deposit_total?: number;

  @ApiPropertyOptional({
    description: 'Total withdraw amount',
    example: 500.25,
  })
  @IsOptional()
  @IsNumber()
  withdraw_total?: number;

  @ApiPropertyOptional({
    description: 'Last withdraw date',
    example: '2023-12-01T10:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  withdraw_at?: string;

  @ApiPropertyOptional({
    description: 'Total bet amount',
    example: 2000.75,
  })
  @IsOptional()
  @IsNumber()
  bet?: number;

  @ApiPropertyOptional({
    description: 'Valid bet amount',
    example: 1800.50,
  })
  @IsOptional()
  @IsNumber()
  valid_bet?: number;

  @ApiPropertyOptional({
    description: 'Payout amount',
    example: 1200.25,
  })
  @IsOptional()
  @IsNumber()
  payout?: number;

  @ApiPropertyOptional({
    description: 'Bonus amount',
    example: 100.00,
  })
  @IsOptional()
  @IsNumber()
  bonus?: number;

  @ApiPropertyOptional({
    description: 'Deduction amount',
    example: 50.00,
  })
  @IsOptional()
  @IsNumber()
  deduction?: number;

  @ApiPropertyOptional({
    description: 'Profit amount',
    example: 150.25,
  })
  @IsOptional()
  @IsNumber()
  profit?: number;

  @ApiPropertyOptional({
    description: 'Last login date',
    example: '2023-12-01T15:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  login_at?: string;

  @ApiPropertyOptional({
    description: 'Join date',
    example: '2023-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  join_at?: string;
}
