import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested, ArrayMinSize } from 'class-validator';
import { CreateMemberStatisticsDto } from './create-member-statistics.dto';

export class BulkCreateMemberStatisticsDto {
  @ApiProperty({
    description: 'Array of member statistics to create',
    type: [CreateMemberStatisticsDto],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateMemberStatisticsDto)
  memberStatistics: CreateMemberStatisticsDto[];
}

export class BulkCreateMemberStatisticsResponseDto {
  @ApiProperty({
    description: 'Total number of records processed',
    example: 100,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Number of successfully created records',
    example: 95,
  })
  successCount: number;

  @ApiProperty({
    description: 'Number of failed records',
    example: 5,
  })
  errorCount: number;

  @ApiProperty({
    description: 'Array of error details for failed records',
    example: [
      {
        index: 10,
        error: 'Username already exists',
        data: { username: 'duplicate_user' }
      }
    ],
  })
  errors: Array<{
    index: number;
    error: string;
    data: Record<string, unknown>;
  }>;

  @ApiProperty({
    description: 'Array of successfully created member statistics IDs',
    example: ['123e4567-e89b-12d3-a456-426614174000'],
  })
  createdIds: string[];
}
