import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class MemberStatisticsResponseDto {
  @ApiProperty({
    description: 'Member Statistics ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Brand ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  brand_id?: string;

  @ApiPropertyOptional({
    description: 'Agency code',
    example: 'AG001',
  })
  @Expose()
  agency_code?: string;

  @ApiPropertyOptional({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username?: string;

  @ApiPropertyOptional({
    description: 'Number of deposit times',
    example: 5,
  })
  @Expose()
  deposit_times?: number;

  @ApiPropertyOptional({
    description: 'Total deposit amount',
    example: 1000.50,
  })
  @Expose()
  deposit_total?: number;

  @ApiPropertyOptional({
    description: 'Total withdraw amount',
    example: 500.25,
  })
  @Expose()
  withdraw_total?: number;

  @ApiPropertyOptional({
    description: 'Last withdraw date',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  withdraw_at?: Date;

  @ApiPropertyOptional({
    description: 'Total bet amount',
    example: 2000.75,
  })
  @Expose()
  bet?: number;

  @ApiPropertyOptional({
    description: 'Valid bet amount',
    example: 1800.50,
  })
  @Expose()
  valid_bet?: number;

  @ApiPropertyOptional({
    description: 'Payout amount',
    example: 1200.25,
  })
  @Expose()
  payout?: number;

  @ApiPropertyOptional({
    description: 'Bonus amount',
    example: 100.00,
  })
  @Expose()
  bonus?: number;

  @ApiPropertyOptional({
    description: 'Deduction amount',
    example: 50.00,
  })
  @Expose()
  deduction?: number;

  @ApiPropertyOptional({
    description: 'Profit amount',
    example: 150.25,
  })
  @Expose()
  profit?: number;

  @ApiPropertyOptional({
    description: 'Last login date',
    example: '2023-12-01T15:30:00Z',
  })
  @Expose()
  login_at?: Date;

  @ApiPropertyOptional({
    description: 'Join date',
    example: '2023-01-01T00:00:00Z',
  })
  @Expose()
  join_at?: Date;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'User who created this record',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'User who last updated this record',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;
}
