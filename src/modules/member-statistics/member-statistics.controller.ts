import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { MemberStatisticsService } from './member-statistics.service';
import { 
  CreateMemberStatisticsDto, 
  UpdateMemberStatisticsDto, 
  MemberStatisticsResponseDto,
  BulkCreateMemberStatisticsDto,
  BulkCreateMemberStatisticsResponseDto
} from './dto';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { plainToClass } from 'class-transformer';
import { CurrentUser } from '@/common/decorators';

@ApiTags('Member Statistics')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller('member-statistics')
export class MemberStatisticsController {
  constructor(private readonly memberStatisticsService: MemberStatisticsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new member statistics record' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Member statistics created successfully',
    type: MemberStatisticsResponseDto,
  })
  async create(
    @Body() createMemberStatisticsDto: CreateMemberStatisticsDto,
    @Request() req: { user?: { username: string } },
    @CurrentUser() currentUser: any,
  ): Promise<MemberStatisticsResponseDto> {
    const memberStatistics = await this.memberStatisticsService.create(createMemberStatisticsDto, currentUser.fullName || currentUser.username);
    return plainToClass(MemberStatisticsResponseDto, memberStatistics, { excludeExtraneousValues: true });
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Bulk create multiple member statistics records' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Member statistics bulk created successfully',
    type: BulkCreateMemberStatisticsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async bulkCreate(
    @Body() bulkCreateMemberStatisticsDto: BulkCreateMemberStatisticsDto,
    @Request() req: { user?: { username: string } },
  ): Promise<BulkCreateMemberStatisticsResponseDto> {
    return await this.memberStatisticsService.bulkCreate(
      bulkCreateMemberStatisticsDto,
      req.user?.username
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all member statistics with pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member statistics retrieved successfully',
    type: [MemberStatisticsResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort by field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(
    @Query() paginationDto: PaginationDto,
  ): Promise<PaginatedResult<MemberStatisticsResponseDto>> {
    const result = await this.memberStatisticsService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(memberStatistics => 
        plainToClass(MemberStatisticsResponseDto, memberStatistics, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a member statistics record by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member statistics found',
    type: MemberStatisticsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Member statistics not found',
  })
  @ApiParam({ name: 'id', description: 'Member statistics ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<MemberStatisticsResponseDto> {
    const memberStatistics = await this.memberStatisticsService.findOne(id);
    return plainToClass(MemberStatisticsResponseDto, memberStatistics, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a member statistics record' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member statistics updated successfully',
    type: MemberStatisticsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Member statistics not found',
  })
  @ApiParam({ name: 'id', description: 'Member statistics ID' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMemberStatisticsDto: UpdateMemberStatisticsDto,
    @Request() req: { user?: { username: string } },
  ): Promise<MemberStatisticsResponseDto> {
    const memberStatistics = await this.memberStatisticsService.update(id, updateMemberStatisticsDto, req.user?.username);
    return plainToClass(MemberStatisticsResponseDto, memberStatistics, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a member statistics record' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Member statistics deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Member statistics not found',
  })
  @ApiParam({ name: 'id', description: 'Member statistics ID' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: { user?: { username: string } },
  ): Promise<void> {
    await this.memberStatisticsService.remove(id, req.user?.username);
  }
}
