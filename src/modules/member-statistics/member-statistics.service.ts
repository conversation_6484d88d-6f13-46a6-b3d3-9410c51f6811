import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MemberStatistics } from '@/entities/member-statistics.entity';
import { Agency } from '@/entities/agency.entity';
import { CreateMemberStatisticsDto } from './dto/create-member-statistics.dto';
import { UpdateMemberStatisticsDto } from './dto/update-member-statistics.dto';
import { BulkCreateMemberStatisticsDto, BulkCreateMemberStatisticsResponseDto } from './dto/bulk-create-member-statistics.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class MemberStatisticsService {
  constructor(
    @InjectRepository(MemberStatistics)
    private readonly memberStatisticsRepository: Repository<MemberStatistics>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createMemberStatisticsDto: CreateMemberStatisticsDto, createdBy?: string): Promise<MemberStatistics> {
    this.logger.info(
      `Creating new member statistics: ${createMemberStatisticsDto.username}`,
      'MemberStatisticsService',
      { username: createMemberStatisticsDto.username, agency_code: createMemberStatisticsDto.agency_code, createdBy }
    );

    try {
      // Validate agency exists if agency_code is provided
      if (createMemberStatisticsDto.agency_code) {
        const agency = await this.agencyRepository.findOne({
          where: { code: createMemberStatisticsDto.agency_code },
        });
        if (!agency) {
          throw new NotFoundException(`Agency with code ${createMemberStatisticsDto.agency_code} not found`);
        }
      }

      const memberStatistics = this.memberStatisticsRepository.create({
        ...createMemberStatisticsDto,
        withdraw_at: createMemberStatisticsDto.withdraw_at ? new Date(createMemberStatisticsDto.withdraw_at) : undefined,
        login_at: createMemberStatisticsDto.login_at ? new Date(createMemberStatisticsDto.login_at) : undefined,
        join_at: createMemberStatisticsDto.join_at ? new Date(createMemberStatisticsDto.join_at) : undefined,
        createdBy,
      });

      const savedMemberStatistics = await this.memberStatisticsRepository.save(memberStatistics);

      this.logger.info(
        `Member statistics created successfully: ${savedMemberStatistics.username}`,
        'MemberStatisticsService',
        { memberStatisticsId: savedMemberStatistics.id, username: savedMemberStatistics.username }
      );

      return savedMemberStatistics;
    } catch (error) {
      this.logger.error(
        `Failed to create member statistics: ${createMemberStatisticsDto.username}`,
        error,
        'MemberStatisticsService'
      );
      throw error;
    }
  }

  async bulkCreate(bulkCreateDto: BulkCreateMemberStatisticsDto, createdBy?: string): Promise<BulkCreateMemberStatisticsResponseDto> {
    this.logger.info(
      `Starting bulk creation of ${bulkCreateDto.memberStatistics.length} member statistics`,
      'MemberStatisticsService',
      { count: bulkCreateDto.memberStatistics.length, createdBy }
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const result: BulkCreateMemberStatisticsResponseDto = {
      totalProcessed: bulkCreateDto.memberStatistics.length,
      successCount: 0,
      errorCount: 0,
      errors: [],
      createdIds: [],
    };

    try {
      for (let i = 0; i < bulkCreateDto.memberStatistics.length; i++) {
        const memberStatisticsDto = bulkCreateDto.memberStatistics[i];
        try {
          // Validate agency exists if agency_code is provided
          if (memberStatisticsDto.agency_code) {
            const agency = await queryRunner.manager.findOne(Agency, {
              where: { code: memberStatisticsDto.agency_code },
            });
            if (!agency) {
              throw new Error(`Agency with code ${memberStatisticsDto.agency_code} not found`);
            }
          }

          const memberStatistics = queryRunner.manager.create(MemberStatistics, {
            ...memberStatisticsDto,
            withdraw_at: memberStatisticsDto.withdraw_at ? new Date(memberStatisticsDto.withdraw_at) : undefined,
            login_at: memberStatisticsDto.login_at ? new Date(memberStatisticsDto.login_at) : undefined,
            join_at: memberStatisticsDto.join_at ? new Date(memberStatisticsDto.join_at) : undefined,
            createdBy,
          });

          const saved = await queryRunner.manager.save(memberStatistics);
          result.createdIds.push(saved.id);
          result.successCount++;
        } catch (error) {
          result.errorCount++;
          result.errors.push({
            index: i,
            error: error.message,
            data: memberStatisticsDto as Record<string, unknown>,
          });
          this.logger.warn(
            `Failed to create member statistics at index ${i}: ${error.message}`,
            'MemberStatisticsService'
          );
        }
      }

      await queryRunner.commitTransaction();

      this.logger.info(
        `Bulk creation completed: ${result.successCount} successful, ${result.errorCount} failed`,
        'MemberStatisticsService',
        result
      );

      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error('Bulk creation failed', error, 'MemberStatisticsService');
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<MemberStatistics>> {
    this.logger.info('Fetching all member statistics', 'MemberStatisticsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.memberStatisticsRepository.createQueryBuilder('memberStats')
        .leftJoinAndSelect('memberStats.agency', 'agency');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`memberStats.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('memberStats.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [memberStatistics, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${memberStatistics.length} member statistics`,
        'MemberStatisticsService',
        { total, page, limit }
      );

      return createPaginatedResult(memberStatistics, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch member statistics', error, 'MemberStatisticsService');
      throw error;
    }
  }

  async findOne(id: string): Promise<MemberStatistics> {
    this.logger.info(`Fetching member statistics by ID: ${id}`, 'MemberStatisticsService');

    try {
      const memberStatistics = await this.memberStatisticsRepository.findOne({
        where: { id },
        relations: ['agency'],
      });

      if (!memberStatistics) {
        this.logger.warn(`Member statistics not found: ${id}`, 'MemberStatisticsService');
        throw new NotFoundException('Member statistics not found');
      }

      this.logger.info(`Member statistics found: ${memberStatistics.username}`, 'MemberStatisticsService', { memberStatisticsId: id });
      return memberStatistics;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch member statistics: ${id}`, error, 'MemberStatisticsService');
      throw error;
    }
  }

  async update(id: string, updateMemberStatisticsDto: UpdateMemberStatisticsDto, updatedBy?: string): Promise<MemberStatistics> {
    this.logger.info(`Updating member statistics: ${id}`, 'MemberStatisticsService', { updateMemberStatisticsDto, updatedBy });

    try {
      const memberStatistics = await this.findOne(id);

      // Validate agency exists if agency_code is being updated
      if (updateMemberStatisticsDto.agency_code) {
        const agency = await this.agencyRepository.findOne({
          where: { code: updateMemberStatisticsDto.agency_code },
        });
        if (!agency) {
          throw new NotFoundException(`Agency with code ${updateMemberStatisticsDto.agency_code} not found`);
        }
      }

      // Update fields
      Object.assign(memberStatistics, {
        ...updateMemberStatisticsDto,
        withdraw_at: updateMemberStatisticsDto.withdraw_at ? new Date(updateMemberStatisticsDto.withdraw_at) : memberStatistics.withdraw_at,
        login_at: updateMemberStatisticsDto.login_at ? new Date(updateMemberStatisticsDto.login_at) : memberStatistics.login_at,
        join_at: updateMemberStatisticsDto.join_at ? new Date(updateMemberStatisticsDto.join_at) : memberStatistics.join_at,
        updatedBy,
      });

      const updatedMemberStatistics = await this.memberStatisticsRepository.save(memberStatistics);

      this.logger.info(
        `Member statistics updated successfully: ${updatedMemberStatistics.username}`,
        'MemberStatisticsService',
        { memberStatisticsId: id }
      );

      return updatedMemberStatistics;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to update member statistics: ${id}`, error, 'MemberStatisticsService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting member statistics: ${id}`, 'MemberStatisticsService', { deletedBy });

    try {
      const memberStatistics = await this.findOne(id);
      
      await this.memberStatisticsRepository.remove(memberStatistics);

      this.logger.info(
        `Member statistics deleted successfully: ${memberStatistics.username}`,
        'MemberStatisticsService',
        { memberStatisticsId: id }
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete member statistics: ${id}`, error, 'MemberStatisticsService');
      throw error;
    }
  }
}
