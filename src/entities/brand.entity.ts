import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { InvoiceProposals } from './invoice-proposals.entity';
import { Department } from './department.entity';
import { Agency } from './agency.entity';

/**
 * Brand Entity
 * Represents brands in the system
 */
@Entity('brands')
export class Brand extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the brand',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the brand',
  })
  description?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the brand is active',
  })
  active: boolean;

  // Relationships
  @OneToMany(() => InvoiceProposals, (proposal) => proposal.brand)
  proposals: InvoiceProposals[];

  @OneToMany(() => Department, (department) => department.brand)
  departments: Department[];

  @OneToMany(() => Agency, (agency) => agency.brand)
  agencies: Agency[];
}
