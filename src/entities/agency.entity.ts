import { Entity, Column, Index, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Brand } from './brand.entity';
import { MemberStatistics } from './member-statistics.entity';
import { User } from './user.entity';

/**
 * Agency Entity
 * Represents agencies in the system
 */
@Entity('agencies')
@Index('idx_agency_code', ['code'], { unique: true })
export class Agency extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Brand ID associated with the agency',
  })
  brand_id: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'User ID associated with the agency',
    nullable: true,
  })
  user_id: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Unique agency code',
  })
  code: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Extra information about the agency',
  })
  extra?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the agency',
  })
  description?: string;

  // Relationships
  @ManyToOne(() => Brand, (brand) => brand.agencies, { nullable: true })
  @JoinColumn({ name: 'brand_id' })
  brand?: Brand;

  // Relationships
  @ManyToOne(() => User, (user) => user.agencies, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @OneToMany(() => MemberStatistics, (memberStats) => memberStats.agency)
  memberStatistics: MemberStatistics[];
}
