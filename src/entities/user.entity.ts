import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, Join<PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserMenuAssignment } from './user-menu-assignment.entity';
import { Team } from './team.entity';
import { Role } from './role.entity';
import { Department } from './department.entity';
import { UserRoleAssignment } from './user-role-assignment.entity';
import { Agency } from './agency.entity';

/**
 * User Entity
 * Represents users in the system
 */
@Entity('users')
export class User extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Unique username for the user',
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Full name of the user',
  })
  fullName: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Email address of the user',
    nullable: true
  })
  email?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Phone number of the user',
  })
  phone?: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Encrypted password',
  })
  password: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Two-factor authentication enabled',
  })
  twoFactorEnabled: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Two-factor authentication secret',
  })
  twoFactorSecret?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Whitelist IP addresses for user access',
  })
  whitelistIPs?: string[];

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Address of the user',
  })
  address?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'City of the user',
  })
  city?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'State of the user',
  })
  state?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'ZIP code of the user',
  })
  zip?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Country of the user',
  })
  country?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Role ID',
  })
  roleId?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Department ID',
  })
  departmentId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Brand access permissions - array of brand IDs',
  })
  brandAccess?: string[];

  // Relationships
  @ManyToOne(() => Role, { nullable: true })
  @JoinColumn({ name: 'roleId' })
  role?: Role;

  @ManyToMany(() => Team, (team) => team.users)
  @JoinTable({
    name: 'user_teams',
    joinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'team_id',
      referencedColumnName: 'id',
    },
  })
  teams: Team[];

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'departmentId' })
  department?: Department;

  @ManyToMany(() => Department, (department) => department.users)
  @JoinTable({
    name: 'user_departments',
    joinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'department_id',
      referencedColumnName: 'id',
    },
  })
  departments: Department[];

  @OneToMany(() => UserMenuAssignment, (menuAssignment) => menuAssignment.user)
  menuAssignments: UserMenuAssignment[];

  @OneToMany(() => UserRoleAssignment, (userRoleAssignment) => userRoleAssignment.user)
  roleAssignments: UserRoleAssignment[];

  @OneToMany(() => Agency, agency => agency.user)
  @JoinColumn({ name: 'agencyId' })
  agencies?: Agency;
}
